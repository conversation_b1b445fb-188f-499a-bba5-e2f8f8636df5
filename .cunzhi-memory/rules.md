# 开发规范和规则

- 用户端分页使用HasMore格式，管理端分页参考其他代码的标准格式
- 会话表设计规则：一条会话记录两条数据，支持已读、删除联系人功能；索引性能考虑用户查询联系人时sql条件为where uid_a=?，联系人表索引为uid_a；消息表一条消息记录一条数据，使用<big_uid, small_uid>联合索引，查询sql为where big_uid=max(uid_a,uid_b) and small_uid=min(uid_a,uid_b)，根据direction字段展示聊天方向
- 项目规范：价格应使用"分"作为单位的整数(bigint)存储，避免浮点数精度问题
- 消息发送频率限制规则：用户向某个接收方连续发送超过5条消息且对方未回复时，提示"你已向 Ta 发送 5 条消息，请耐心等待回复～"并阻止发送；对方回复后重置计数；不考虑时间窗口和消息类型- 消息发送频率限制实现方案：会话表增加is_frequency_limited标记字段，发送消息时判断标记，有标记则查询消息数量判断是否拦截，对方回复时清除标记，避免每次都查询数据库提高性能
- 异步处理规范：使用spanCtx := s.NewContextWithSpanContext(s.ctx) + go func() 异步操作，确保链路追踪的连续性
